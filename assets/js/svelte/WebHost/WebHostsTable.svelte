<script lang="ts">
	import { onMount } from 'svelte';
	import { writable } from 'svelte/store';
	import {
		createSvelteTable,
		flexRender,
		getCoreRowModel,
		getSortedRowModel,
		getFilteredRowModel,
		getExpandedRowModel,
		renderComponent
	} from '@tanstack/svelte-table';
	import type {
		ColumnDef,
		TableOptions,
		SortingState,
		ExpandedState
	} from '@tanstack/svelte-table';

	import { getWebHosts } from '../../api';

	// Components
	import WebHostType from './Cells/WebHostType.svelte';
	import WebHostEnvironment from './Cells/WebHostEnvironment.svelte';
	import WebHostVisibility from './Cells/WebHostVisibility.svelte';
	import WebHostUrls from './Cells/WebHostUrls.svelte';
	import WebHostHealth from './Cells/WebHostHealth.svelte';
	import WebHostSSL from './Cells/WebHostSSL.svelte';
	import WebHostActions from './Cells/WebHostActions.svelte';
	import WebHostExpandButton from './Cells/WebHostExpandButton.svelte';
	import WebHostUrlRow from './Cells/WebHostUrlRow.svelte';
	import { debounce } from 'lodash';
	import WebHostAssociatedUrls from '~/js/svelte/WebHost/Cells/WebHostAssociatedUrls.svelte';
	import { mercureEventSource, selectedWebHosts, waitingForWebHostUpdates } from '../stores';
	import WebHostUpdateBar from './WebHostUpdateBar.svelte';
	import WebHostSelectCell from './Cells/WebHostSelectCell.svelte';
	import Php from "~/js/svelte/Cells/Php.svelte";
	import Symfony from "~/js/svelte/Cells/Symfony.svelte";
	import { getSemVerAccessor } from "~/js/utils.ts";
	import DetailsModal from "~/js/svelte/DetailsModal.svelte";

	let loading = $state(false);
	let webHosts: WebHost[] = $state([]);
	let loadingErrors: object[] = $state([]);
	let modalOpen = $state(false);
	let modalService: Service | null = $state(null);
	let searchValue: string = $state('');

	// TanStack Table state
	let sorting: SortingState = $state([]);
	let globalFilter = $state('');
	let expanded: ExpandedState = $state({});

	const setSorting = (updater: any) => {
		if (typeof updater === 'function') {
			sorting = updater(sorting);
		} else {
			sorting = updater;
		}
	};

	const setExpanded = (updater: any) => {
		if (typeof updater === 'function') {
			expanded = updater(expanded);
		} else {
			expanded = updater;
		}
	};

	function showDetails(service: Service) {
		modalOpen = true;
		modalService = service;
	}

	// Fonctions de sélection
	const selectWebHost = (webHost: WebHost) => {
		const index = $selectedWebHosts.findIndex((s) => s.id === webHost.id);
		if (index === -1) {
			$selectedWebHosts = [...$selectedWebHosts, webHost];
		} else {
			$selectedWebHosts = $selectedWebHosts.filter((s) => s.id !== webHost.id);
		}
	};

	const selectAll = () => {
		if (isAllSelected) {
			$selectedWebHosts = [];
		} else {
			$selectedWebHosts = [...$table.getRowModel().rows.map((row) => row.original)];
		}
	};

	// Colonnes du tableau
	const columns: ColumnDef<WebHost>[] = [
		{
			id: 'select',
			header: '',
			enableSorting: false,
			cell: (props) =>
				renderComponent(WebHostSelectCell, {
					webHost: props.row.original
				})
		},
		{
			id: 'expand',
			header: '',
			enableSorting: false,
			cell: (props) =>
				renderComponent(WebHostExpandButton, {
					webHost: props.row.original,
					row: props.row
				})
		},
		{
			id: 'name',
			accessorKey: 'name',
			header: 'Nom',
			cell: (info) => info.getValue()
		},
		{
			id: 'type',
			accessorFn: (webHost: WebHost) => webHost.configuration?.type || '',
			header: 'Type',
			cell: (props) =>
				renderComponent(WebHostType, {
					webHost: props.row.original
				})
		},
		{
			id: 'environment',
			accessorKey: 'environnement',
			header: 'Environnement',
			cell: (props) =>
				renderComponent(WebHostEnvironment, {
					webHost: props.row.original
				})
		},
		{
			id: 'webId',
			accessorFn: (webHost: WebHost) => webHost.configuration?.webId || '',
			header: 'Web ID',
			cell: (props) => props.getValue()
		},
		{
			id: 'links',
			accessorKey: 'associatedUrls',
			header: 'Liens associés',
			cell: (props) =>
				renderComponent(WebHostAssociatedUrls, {
					webHost: props.row.original
				})
		},
		{
			id: 'php',
			accessorFn: (webHost: WebHost) => {
				return webHost?.service?.details?.php?.version ?? 0;
			},
			sortingFn: (rowA, rowB) => {
				return getSemVerAccessor(rowA.original.service, rowA.original.service?.details?.php?.version).localeCompare(
					getSemVerAccessor(rowB.original.service, rowB.original.service?.details?.php?.version)
				);
			},
			header: `PHP`,
			cell: (props) =>
				renderComponent(Php, {
					service: props.row.original.service
				}),
		},
		{
			id: 'symfony',
			accessorFn: (webHost: WebHost) => webHost?.service?.details?.symfony?.version,
			sortingFn: (rowA, rowB) => {
				return getSemVerAccessor(
					rowA.original.service,
					rowA.original.service?.details?.symfony?.version
				).localeCompare(getSemVerAccessor(rowB.original.service, rowB.original.service?.details?.symfony?.version));
			},
			header: `<div class="text-center"><i class="fa-brands fa-symfony fs-5"></i></div>`,
			cell: (props) =>
				renderComponent(Symfony, {
					service: props.row.original.service
				})
		},
		{
			id: 'visibility',
			accessorKey: 'expectedVisibility',
			header: 'Visibilité',
			cell: (props) =>
				renderComponent(WebHostVisibility, {
					webHost: props.row.original
				})
		},
		{
			id: 'urls',
			accessorFn: (webHost: WebHost) => webHost.urls.map((u) => u.url).join(' '),
			header: 'URL',
			enableSorting: false,
			cell: (props) =>
				renderComponent(WebHostUrls, {
					webHost: props.row.original
				})
		},
		{
			id: 'health',
			accessorFn: (webHost: WebHost) => {
				const statuses = webHost.urls
					.map((u) => u.healthCheckReport?.status)
					.filter((status) => status !== null && status !== undefined && status !== '');
				if (statuses.length === 0) return null;
				if (statuses.includes('CRITICAL')) return 'CRITICAL';
				if (statuses.includes('WARNING')) return 'WARNING';
				if (statuses.includes('OK')) return 'OK';
				return null;
			},
			header: 'Santé',
			cell: (props) =>
				renderComponent(WebHostHealth, {
					webHost: props.row.original
				})
		},
		{
			id: 'ssl',
			accessorFn: (webHost: WebHost) => {
				const validStatuses = webHost.urls
					.map((u) => u.sslCertificateReport?.isValid)
					.filter((status) => status !== null && status !== undefined);
				if (validStatuses.length === 0) return null;
				return validStatuses.includes(false) ? 'INVALID' : 'VALID';
			},
			header: 'SSL',
			cell: (props) =>
				renderComponent(WebHostSSL, {
					webHost: props.row.original
				})
		},
		{
			id: 'actions',
			header: 'Détails',
			enableSorting: false,
			cell: (props) =>
				renderComponent(WebHostActions, {
					webHost: props.row.original,
					showDetails
				})
		}
	];

	// Configuration TanStack Table
	const tableOptions = writable<TableOptions<WebHost>>({
		data: [],
		columns,
		state: {
			sorting,
			expanded
		},
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getExpandedRowModel: getExpandedRowModel(),
		onSortingChange: setSorting,
		onExpandedChange: setExpanded,
		enableColumnFilter: false,
		getRowCanExpand: (row) => row.original.urls.length > 1
	});

	const table = createSvelteTable(tableOptions);

	const handleSearch = debounce((e: any) => {
		$table.setGlobalFilter(String(e?.target?.value));
	}, 100);

	// Mise à jour des données de la table
	$effect(() => {
		tableOptions.update((options) => ({
			...options,
			data: webHosts,
			state: {
				...options.state,
				sorting,
				expanded
			}
		}));
	});

	// Chargement des données
	const loadWebHosts = async () => {
		loading = true;
		loadingErrors = [];
		try {
			const result = await getWebHosts();
			webHosts = result.webHosts;
		} catch (error) {
			loadingErrors = [{ message: 'Erreur lors du chargement des hébergements' }];
			console.error('Erreur lors du chargement des hébergements:', error);
		} finally {
			loading = false;
		}
	};

	let eventSourceInitialized = false;

	onMount(() => {
		loadWebHosts();

		// Écouter les mises à jour Mercure
		if (!eventSourceInitialized) {
			$mercureEventSource.addEventListener('message', (event) => {
				let message = JSON.parse(event.data);
				if (message.topic === 'webhost.updated') {
					// Trouver et mettre à jour le WebHost correspondant
					const webHostIndex = webHosts.findIndex((wh) => wh.id === message.data.id);
					if (webHostIndex !== -1) {
						// Mettre à jour seulement les URLs avec les nouveaux rapports
						webHosts[webHostIndex].urls = message.data.urls;
					}
				}
			});
			eventSourceInitialized = true;
		}
	});

	// Gestion du comptage des éléments
	let tableData = $derived($table.getRowModel().rows.map((row) => row.original));
	let countsWebHosts = $derived(tableData.length);

	let sortedColumn = $derived(sorting[0]?.id);
	let sortedDirection = $derived(sorting[0]?.desc ? 'desc' : 'asc');

	// Gestion de la sélection
	let isAllSelected = $derived(
		$selectedWebHosts.length === $table.getRowModel().rows.length &&
			$table.getRowModel().rows.length > 0
	);

	// Gestion du statut de la mise à jour
	let waitingIds = $derived(
		$waitingForWebHostUpdates.filter((w) => w.waiting).map((w) => w.webHostId)
	);
	let updatedIds = $derived(
		$waitingForWebHostUpdates.filter((w) => !w.waiting).map((w) => w.webHostId)
	);
	let hasErrorIds = $derived(
		$waitingForWebHostUpdates.filter((w) => w.error).map((w) => w.webHostId)
	);
</script>

{#if loadingErrors.length > 0}
	<div class="alert alert-danger d-flex align-items-center" role="alert">
		<i class="fa fa-info-circle me-2"></i>
		<div>
			{#each loadingErrors as error}
				{error.message}
			{/each}
		</div>
	</div>
{/if}

<div class="table-responsive">
	<div class="gridjs gridjs-container" style="width: 100%">
		<div class="d-flex align-items-center justify-content-between mb-3">
			<div class="d-flex align-items-center">
				{#if !loading}
					<div class="me-2">
						{countsWebHosts} hébergement{countsWebHosts > 1 ? 's' : ''}
					</div>
					{#if tableData.length > 0}
						<button class="btn btn-sm btn-info text-white me-2" onclick={selectAll}
							>Tout {isAllSelected ? 'désélectionner' : 'sélectionner'}</button
						>
					{/if}
				{/if}
			</div>
			<div>
				<a href="/webHosts/create" class="btn btn-sm btn-info text-white me-2">
					Ajouter un hébergement
				</a>
				<button
					class="btn btn-sm btn-info text-white"
					onclick={() => loadWebHosts()}
					disabled={loading}
				>
					{#if loading}
						<i class="fa-solid fa-circle-notch fa-spin"></i>
					{:else}
						<i class="fa-solid fa-arrows-rotate"></i>
					{/if}
					Actualiser
				</button>
			</div>
		</div>
		<div class="d-flex align-items-center mb-3">
			<div class="gridjs-search me-2">
				<div class="input-group">
					<input
						bind:value={searchValue}
						oninput={handleSearch}
						type="text"
						placeholder="Rechercher..."
						class="gridjs-input gridjs-search-input"
					/>
					{#if searchValue !== ''}
						<span
							class="input-group-text"
							type="button"
							onclick={() => {
								searchValue = '';
								globalFilter = '';
								$table.setGlobalFilter('');
							}}
						>
							<i class="fa fa-times"></i>
						</span>
					{:else}
						<span class="input-group-text">
							<i class="fa fa-magnifying-glass"></i>
						</span>
					{/if}
				</div>
			</div>
			<div class="flex-grow-1"></div>
			<WebHostUpdateBar />
		</div>
		<div class="gridjs-wrapper" style="height: auto">
			<table role="grid" class="gridjs-table" style="min-width: 100%; height: auto">
				<thead class="gridjs-head">
					{#each $table.getHeaderGroups() as headerGroup}
						<tr class="gridjs-tr">
							{#each headerGroup.headers as header}
								<th
									onclick={header.column.getToggleSortingHandler()}
									class="gridjs-th"
									class:gridjs-th-sort={header.column.getCanSort()}
									class:cursor-pointer={header.column.getCanSort()}
									style={header.id === 'expand' ? 'width: 48px;' : ''}
								>
									<div class="gridjs-th-content">
										{@html header.column.columnDef.header}
									</div>
									{#if header.column.getCanSort()}
										<button
											title="Sort column"
											class="gridjs-sort gridjs-sort-{sortedColumn === header.column.id
												? sortedDirection
												: 'neutral'}"
										></button>
									{/if}
								</th>
							{/each}
						</tr>
					{/each}
				</thead>
				<tbody class="gridjs-tbody">
					{#if loading}
						<tr class="gridjs-tr">
							<td colspan="100%" class="gridjs-td">
								<div class="d-flex justify-content-center">
									<div class="spinner-border text-info m-5" role="status">
										<span class="visually-hidden">Loading...</span>
									</div>
								</div>
							</td>
						</tr>
					{:else if $table.getRowModel().rows.length === 0}
						<tr class="gridjs-tr">
							<td colspan="100%" class="gridjs-td text-center"> Aucun hébergement trouvé </td>
						</tr>
					{:else}
						{#each $table.getRowModel().rows as row (row.original.id)}
							<tr
								class="gridjs-tr"
								class:webhost-reloading={waitingIds.includes(row.original.id)}
								data-id={row.original.id}
							>
								{#each row.getVisibleCells() as cell}
									<td class="gridjs-td">
										<svelte:component
											this={flexRender(cell.column.columnDef.cell, cell.getContext())}
										/>
									</td>
								{/each}
							</tr>
							<!-- Lignes d'expansion pour les URLs multiples -->
							{#if row.getIsExpanded() && row.original.urls.length > 1}
								{#each row.original.urls as url}
									<tr class="gridjs-tr" style="background-color: #f8f9fa;">
										<WebHostUrlRow {url} columnsCount={columns.length} />
									</tr>
								{/each}
							{/if}
						{/each}
					{/if}
				</tbody>
			</table>
		</div>
	</div>
</div>

<DetailsModal service={modalService} bind:isOpen={modalOpen} />
