<script lang="ts">
	let { message }: { message: string } = $props();
</script>

<div class="error">
	<strong>Erreur:</strong> {message}
</div>

<style>
	.error {
		background: linear-gradient(135deg, #ff6b6b, #ee5a52);
		color: white;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 16px;
		text-align: center;
	}

	.error::before {
		content: '⚠️';
		display: block;
		font-size: 20px;
		margin-bottom: 4px;
	}
</style>
