<script lang="ts">
	import type { ServiceDetails, ServiceRepository } from '@/popup/types.js';
	import { convertGitlabImageUrlToHttp } from '@/popup/utils';

	let { 
		serviceDetails, 
		serviceRepository 
	}: { 
		serviceDetails?: ServiceDetails; 
		serviceRepository?: ServiceRepository; 
	} = $props();

	// Calculer si on a des informations à afficher
	let hasInfo = $derived(
		(serviceDetails?.php) || 
		(serviceDetails?.symfony) || 
		(serviceRepository?.image)
	);

	let dockerImage = $derived(serviceRepository?.image.replace('gitlab.alienor.net:5050/', '') || 'N/A');
	let isGitlabImage = $derived(serviceRepository?.image?.includes('gitlab.alienor.net'));
	let imageLink = $derived.by(() => {
		if (!serviceRepository?.image) {
			return null;
		}
		if (isGitlabImage) {
			return convertGitlabImageUrlToHttp(serviceRepository?.name);
		}
		return 'https://hub.docker.com/r/' + serviceRepository.image;
	})
</script>

{#if hasInfo}
	<div class="tech-info">
		<div class="tech-title">⚙️ Technologies</div>
		<div class="tech-details">
			{#if serviceDetails?.php}
				<div class="tech-item">
					<span class="tech-label">PHP :</span>
					<span class="tech-value">{serviceDetails.php.version}</span>
					{#if serviceDetails.php.is_eoled}
						<span class="tech-status eol" title="Version en fin de vie">EOL</span>
					{:else if !serviceDetails.php.is_latest}
						<span 
							class="tech-status outdated" 
							title="Dernière version: {serviceDetails.php.latest_patch_version}"
						>
							Ancienne
						</span>
					{:else}
						<span class="tech-status current">À jour</span>
					{/if}
				</div>
			{/if}

			{#if serviceDetails?.symfony}
				<div class="tech-item">
					<span class="tech-label">Symfony :</span>
					<span class="tech-value">{serviceDetails.symfony.version}</span>
					<a style="text-decoration: none;" href="https://symfony.com/releases/{serviceDetails.symfony.version}" target="_blank">
						{#if serviceDetails.symfony.is_eoled}
							<span
								class="tech-status eol"
								title="Fin de vie: {serviceDetails.symfony.eol}"
							>
								EOL
							</span>
						{:else if serviceDetails.symfony.latest_patch_version !== serviceDetails.symfony.version}
							<span
								class="tech-status outdated"
								title="Fin de maintenance: {serviceDetails.symfony.eom}"
							>
								À mettre à jour
							</span>
						{:else if !serviceDetails.symfony.is_latest}
							<span
								class="tech-status outdated"
								title="Dernière version: {serviceDetails.symfony.latest_patch_version}"
							>
								Ancienne
							</span>
						{:else}
							<span class="tech-status current">À jour</span>
						{/if}
					</a>
				</div>
			{/if}

			{#if serviceRepository?.image}
				<div class="tech-item">
					<span class="tech-label">Image :</span>
					<span 
						class="tech-value" 
						title={serviceRepository.image}
					>
						{dockerImage}
					</span>
					{#if imageLink}
						<a style="text-decoration: none;" href={imageLink} target="_blank">
							{#if isGitlabImage}
								🦊
							{:else}
								🐳
							{/if}
						</a>
					{/if}
				</div>
			{/if}
		</div>
	</div>
{/if}

<style>
	.tech-info {
		background: #f8f9fa;
		border: 1px solid #e9ecef;
		border-radius: 8px;
		padding: 12px;
		margin-bottom: 16px;
	}

	.tech-title {
		font-size: 12px;
		font-weight: 600;
		color: #495057;
		margin-bottom: 8px;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.tech-details {
		display: grid;
		gap: 6px;
	}

	.tech-item {
		display: flex;
		align-items: center;
		font-size: 11px;
	}

	.tech-label {
		font-weight: 500;
		color: #6c757d;
		min-width: 80px;
		margin-right: 8px;
	}

	.tech-value {
		color: #495057;
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		background: #e9ecef;
		padding: 2px 6px;
		border-radius: 4px;
		font-size: 10px;
	}

	.tech-status {
		margin-left: 6px;
		font-size: 9px;
		padding: 1px 4px;
		border-radius: 3px;
		font-weight: 500;
	}

	.tech-status.outdated {
		background: #fff3cd;
		color: #856404;
	}

	.tech-status.eol {
		background: #f8d7da;
		color: #721c24;
	}

	.tech-status.current {
		background: #d4edda;
		color: #155724;
	}
</style>
