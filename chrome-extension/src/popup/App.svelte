<script lang="ts">
	import { onMount } from 'svelte';
	import type { WebHost } from './types.js';
	import { getCurrentTabUrl, fetchWebHosts, findMatchingWebHost } from './utils.js';
	import { debugLog } from './config.js';
	import { getSettings, type ExtensionSettings } from '@/services/settings';

	import LoadingState from '@/components/LoadingState.svelte';
	import ErrorState from '@/components/ErrorState.svelte';
	import NoMatchState from '@/components/NoMatchState.svelte';
	import WebHostCard from '@/components/WebHostCard.svelte';

	// État de l'application
	let currentUrl = $state('Chargement...');
	let loading = $state(true);
	let error = $state<string | null>(null);
	let matchingWebHost = $state<WebHost | null>(null);
	let settings = $state<ExtensionSettings>({ dockerToolsEnabled: true });

	// Fonction principale
	async function main() {
		try {
			loading = true;
			error = null;

			// Charger les paramètres
			settings = await getSettings();
			debugLog('Paramètres chargés:', settings);

			// Obtenir l'URL de l'onglet actif
			const url = await getCurrentTabUrl();
			debugLog("URL de l'onglet actif:", url);
			currentUrl = url;

			// Récupérer les données des webhosts
			const webHosts = await fetchWebHosts();
			debugLog('Nombre de webhosts récupérés:', webHosts.length);

			// Trouver le webhost correspondant
			const webHost = findMatchingWebHost(url, webHosts);

			if (webHost) {
				debugLog('Webhost correspondant trouvé:', webHost.name);
				matchingWebHost = webHost;
			} else {
				debugLog('Aucun webhost correspondant trouvé');
				matchingWebHost = null;
			}
		} catch (err) {
			debugLog('Erreur dans main():', err);
			error = err instanceof Error ? err.message : 'Une erreur inconnue est survenue';
		} finally {
			loading = false;
		}
	}

	// Ouvrir la page de configuration
	function openOptions() {
		chrome.runtime.openOptionsPage();
	}

	// Lancer l'application au montage du composant
	onMount(() => {
		main();
	});
</script>

<div class="header">
    <div class="header-content">
        <h1>Alienor WebHost Extension</h1>
        <button class="settings-btn" onclick={openOptions} title="Configuration">
            ⚙️
        </button>
    </div>
</div>
<div class="container">
	<div class="content">
		{#if loading}
			<LoadingState />
		{:else if error}
			<ErrorState message={error} />
		{:else if matchingWebHost}
			<WebHostCard webHost={matchingWebHost} {settings} />
		{:else}
			<NoMatchState />
		{/if}
	</div>
</div>

<style>
	:global(*) {
		box-sizing: border-box;
	}

	:global(body) {
		width: 420px;
		margin: 0;
		padding: 0;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
		font-size: 13px;
		line-height: 1.4;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #333;
	}

	.container {
		background: white;
        border-radius: 12px;
		margin: 0 8px 8px 8px;
		overflow: hidden;
	}

	.header {
		color: white;
		padding: 8px;
	}

	.header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.header h1 {
		font-size: 14px;
		font-weight: 600;
		text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
		margin: 0;
		flex: 1;
		text-align: center;
	}

	.settings-btn {
		background: none;
		border: none;
		color: white;
		font-size: 16px;
		cursor: pointer;
		padding: 4px;
		border-radius: 4px;
		transition: background-color 0.2s;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 28px;
		height: 28px;
	}

	.settings-btn:hover {
		background-color: rgba(255, 255, 255, 0.1);
	}

	.content {
		padding: 16px;
	}
</style>
