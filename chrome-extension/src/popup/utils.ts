import { CONFIG, debugLog } from './config.js';
import type { WebHost, ApiResponse } from './types.js';

// Fonction pour obtenir l'URL de l'onglet actif
export async function getCurrentTabUrl(): Promise<string> {
	const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
	return tab.url || '';
}

// Fonction pour récupérer les données des webhosts (utilise le cache du service worker)
export async function fetchWebHosts(): Promise<WebHost[]> {
	try {
		debugLog('Récupération des webhosts depuis le cache du service worker');

		// Demander les données au service worker (qui gère le cache)
		const response = await chrome.runtime.sendMessage({ action: 'getCachedWebHosts' });

		if (response && response.webHosts) {
			debugLog('Données reçues du service worker:', response.webHosts.length, 'webhosts');
			return response.webHosts;
		}

		// Fallback : récupération directe si le service worker ne répond pas
		debugLog("Fallback: récupération directe depuis l'API");
		return await fetchWebHostsDirect();
	} catch (error) {
		debugLog('Erreur lors de la récupération depuis le service worker, tentative directe:', error);
		return await fetchWebHostsDirect();
	}
}

// Fonction de fallback pour récupération directe
async function fetchWebHostsDirect(): Promise<WebHost[]> {
	try {
		debugLog('Récupération directe depuis:', `${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`);

		const response = await fetch(`${CONFIG.API_BASE_URL}${CONFIG.API_ENDPOINT}`, {
			method: 'GET',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json'
			}
		});

		if (!response.ok) {
			throw new Error(`HTTP ${response.status}: ${response.statusText}`);
		}

		const data: ApiResponse = await response.json();
		debugLog('Données reçues directement:', data);

		return data.webHosts || [];
	} catch (error) {
		debugLog('Erreur lors de la récupération directe des webhosts:', error);

		// Gestion spécifique des erreurs CORS
		if (error instanceof Error && (error.message.includes('CORS') || error.message.includes('fetch'))) {
			throw new Error(
				`Impossible de se connecter à l'API (${CONFIG.API_BASE_URL}). Vérifiez que l'API est accessible et que les permissions CORS sont configurées.`
			);
		}

		throw error;
	}
}

// Fonction pour trouver le webhost correspondant à l'URL
export function findMatchingWebHost(currentUrl: string, webHosts: WebHost[]): WebHost | null {
	const currentDomain = extractDomain(currentUrl);
	debugLog('Recherche du webhost pour le domaine:', currentDomain);

	for (const webHost of webHosts) {
		debugLog('Vérification du webhost:', webHost.name);

		// Vérifier les URLs principales
		if (webHost.urls && webHost.urls.length > 0) {
			for (const urlObj of webHost.urls) {
				const webhostDomain = extractDomain(urlObj.url);
				debugLog('  - URL principale:', urlObj.url, '-> domaine:', webhostDomain);
				if (webhostDomain === currentDomain) {
					debugLog('✓ Correspondance trouvée avec URL principale');
					return webHost;
				}
			}
		}

		// Vérifier aussi les URLs associées
		if (webHost.associatedUrls && webHost.associatedUrls.length > 0) {
			for (const associatedUrl of webHost.associatedUrls) {
				const associatedDomain = extractDomain(associatedUrl.url);
				debugLog('  - URL associée:', associatedUrl.url, '-> domaine:', associatedDomain);
				if (associatedDomain === currentDomain) {
					debugLog('✓ Correspondance trouvée avec URL associée');
					return webHost;
				}
			}
		}
	}

	debugLog('✗ Aucune correspondance trouvée');
	return null;
}

// Fonction pour extraire le domaine d'une URL
export function extractDomain(url: string): string {
	try {
		const urlObj = new URL(url);
		return urlObj.hostname;
	} catch (error) {
		debugLog('URL invalide:', url, error);
		return '';
	}
}

// Fonction pour convertir une URL Git SSH en URL HTTP
export function convertGitUrlToHttp(gitUrl: string): string {
	if (!gitUrl) {
		return gitUrl;
	}

	// Si c'est déjà une URL HTTP/HTTPS, la retourner telle quelle
	if (gitUrl.startsWith('http://') || gitUrl.startsWith('https://')) {
		return gitUrl;
	}

	// Si ce n'est pas une URL Git SSH, la retourner telle quelle
	if (!gitUrl.startsWith('git@')) {
		return gitUrl;
	}

	try {
		debugLog('Conversion URL Git SSH:', gitUrl);

		// Extraire la partie après git@
		const withoutPrefix = gitUrl.substring(4); // Enlever "git@"

		// Séparer hostname et path
		const colonIndex = withoutPrefix.indexOf(':');
		if (colonIndex === -1) {
			debugLog('Format Git SSH invalide (pas de :):', gitUrl);
			return gitUrl;
		}

		const hostname = withoutPrefix.substring(0, colonIndex);
		let path = withoutPrefix.substring(colonIndex + 1);

		// Gérer le cas où il y a un port (ex: git@hostname:22/path)
		if (path.match(/^\d+\//)) {
			// Enlever le port du path
			const slashIndex = path.indexOf('/');
			if (slashIndex !== -1) {
				path = path.substring(slashIndex + 1);
			}
		}

		// Enlever l'extension .git si présente
		if (path.endsWith('.git')) {
			path = path.substring(0, path.length - 4);
		}

		// Construire l'URL HTTP
		const httpUrl = `https://${hostname}/${path}`;

		debugLog('URL Git convertie:', gitUrl, '->', httpUrl);
		return httpUrl;
	} catch (error) {
		debugLog("Erreur lors de la conversion de l'URL Git:", error);
		return gitUrl; // En cas d'erreur, retourner l'URL originale
	}
}

export function convertGitlabImageUrlToHttp(image: string): string {
	return image.replace('gitlab.alienor.net:5050/', 'https://gitlab.alienor.net/') + '/container_registry';
}

// Fonction pour obtenir l'icône d'un lien
export function getLinkIcon(type: string): string {
	const icons: Record<string, string> = {
		gitlab: '🦊',
		confluence: '📖',
		database: '🗄️',
		url: '🌐',
		associated: '🔗',
		docker: '🐳'
	};

	return icons[type] || '🔗';
}
